# Hide some info on response headers
ServerTokens ProductOnly

<VirtualHost *:${APACHE_PORT_OVERRIDE}>
	# The ServerName directive sets the request scheme, hostname and port that
	# the server uses to identify itself. This is used when creating
	# redirection URLs. In the context of virtual hosts, the ServerName
	# specifies what hostname must appear in the request's Host: header to
	# match this virtual host. For the default virtual host (this file) this
	# value is not decisive as it is used as a last resort host regardless.
	# However, you must set it for any further virtual host explicitly.
	#ServerName www.example.com
	Header set Access-Control-Allow-Origin ${ACCESS_CONTROL_ORIGIN}
	Header set Access-Control-Allow-Credentials true
	
	ServerAdmin webmaster@localhost
	DocumentRoot /var/www/html/src

	# Available loglevels: trace8, ..., trace1, debug, info, notice, warn,
	# error, crit, alert, emerg.
	# It is also possible to configure the loglevel for particular
	# modules, e.g.
	#LogLevel info ssl:warn

	ErrorLog ${APACHE_LOG_DIR}/error.log
	CustomLog ${APACHE_LOG_DIR}/access.log combined

	# For most configuration files from conf-available/, which are
	# enabled or disabled at a global level, it is possible to
	# include a line for only one particular virtual host. For example the
	# following line enables the CGI configuration for this host only
	# after it has been globally disabled with "a2disconf".
	#Include conf-available/serve-cgi-bin.conf

	Alias /fuel_debug /var/www/html/src/fuel_debug
	Alias /fuel /var/www/html/src/fuel

	<Directory "/var/www/html/src/fuel_debug">
		Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
		Header set Access-Control-Allow-Methods "POST, GET, OPTIONS"
		DirectoryIndex index.py
      	Options +ExecCGI
   		AddHandler cgi-script .cgi .pl .py
   		Order allow,deny
   		Allow from all

	</Directory>

		<Directory "/var/www/html/src/fuel">
		Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
		Header set Access-Control-Allow-Methods "POST, GET, OPTIONS"
		DirectoryIndex index.py
      	Options +ExecCGI
   		AddHandler cgi-script .cgi .pl .py
   		Order allow,deny
   		Allow from all

	</Directory>

	# Alias all URLs but static/ to our entrypoint. No redirects will be done
	AliasMatch ^/(?!static/).*$ /var/www/html/src/jsonrpc/public/index.php

	# Pass the Authorization header
	SetEnvIf Authorization "(.*)" Authorization=$1

	SetInputFilter DEFLATE
        SetOutputFilter DEFLATE

        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE image/svg+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/atom_xml
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/x-httpd-php
        AddOutputFilterByType DEFLATE application/x-httpd-fastphp
        AddOutputFilterByType DEFLATE application/x-httpd-eruby
        AddOutputFilterByType DEFLATE text/html

        BrowserMatch ^Mozilla/4 gzip-only-text/html
        BrowserMatch ^Mozilla/4\.0[678] no-gzip
        BrowserMatch \bMSIE !no-gzip !gzip-only-text/html

        # Don't compress images
        SetEnvIfNoCase Request_URI \\.(?:gif|jpe?g|png|swf|swz)$ no-gzip dont-vary
        # or zip files
        SetEnvIfNoCase Request_URI \.zip$ no-gzip dont-vary

        # Make sure proxies don't deliver the wrong content
        Header append Vary User-Agent env=!dont-vary

        DeflateCompressionLevel 5
        DeflateFilterNote ratio

	<Directory "/var/www/html/src/jsonrpc/public">
		Header set Access-Control-Allow-Methods POST
	</Directory>

</VirtualHost>

<VirtualHost *:443>
	# The ServerName directive sets the request scheme, hostname and port that
	# the server uses to identify itself. This is used when creating
	# redirection URLs. In the context of virtual hosts, the ServerName
	# specifies what hostname must appear in the request's Host: header to
	# match this virtual host. For the default virtual host (this file) this
	# value is not decisive as it is used as a last resort host regardless.
	# However, you must set it for any further virtual host explicitly.
	#ServerName www.example.com
	Header set Access-Control-Allow-Origin ${ACCESS_CONTROL_ORIGIN}
	Header set Access-Control-Allow-Credentials true
	
	ServerAdmin webmaster@localhost
	DocumentRoot /var/www/html/src

	# Available loglevels: trace8, ..., trace1, debug, info, notice, warn,
	# error, crit, alert, emerg.
	# It is also possible to configure the loglevel for particular
	# modules, e.g.
	#LogLevel info ssl:warn

	ErrorLog ${APACHE_LOG_DIR}/error.log
	CustomLog ${APACHE_LOG_DIR}/access.log combined

	Alias /fuel_debug /var/www/html/src/fuel_debug
	Alias /fuel /var/www/html/src/fuel

	<Directory "/var/www/html/src/fuel_debug">
		Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
		Header set Access-Control-Allow-Methods "POST, GET, OPTIONS"
		DirectoryIndex index.py
      	Options +ExecCGI
   		AddHandler cgi-script .cgi .pl .py
   		Order allow,deny
   		Allow from all

	</Directory>

	<Directory "/var/www/html/src/fuel">
		Header set Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept"
		Header set Access-Control-Allow-Methods "POST, GET, OPTIONS"
		DirectoryIndex index.py
      	Options +ExecCGI
   		AddHandler cgi-script .cgi .pl .py
   		Order allow,deny
   		Allow from all

	</Directory>

	# For most configuration files from conf-available/, which are
	# enabled or disabled at a global level, it is possible to
	# include a line for only one particular virtual host. For example the
	# following line enables the CGI configuration for this host only
	# after it has been globally disabled with "a2disconf".
	#Include conf-available/serve-cgi-bin.conf

	# Alias all URLs but static/ to our entrypoint. No redirects will be done
	AliasMatch ^/(?!static/).*$ /var/www/html/src/jsonrpc/public/index.php

	# Pass the Authorization header
	SetEnvIf Authorization "(.*)" Authorization=$1

	SSLEngine on
	SSLCertificateFile /opt/certs/server.crt
	SSLCertificateKeyFile /opt/certs/server.key

	SetInputFilter DEFLATE
        SetOutputFilter DEFLATE

        AddOutputFilterByType DEFLATE text/plain
        AddOutputFilterByType DEFLATE text/xml
        AddOutputFilterByType DEFLATE application/xhtml+xml
        AddOutputFilterByType DEFLATE text/css
        AddOutputFilterByType DEFLATE application/xml
        AddOutputFilterByType DEFLATE image/svg+xml
        AddOutputFilterByType DEFLATE application/rss+xml
        AddOutputFilterByType DEFLATE application/atom_xml
        AddOutputFilterByType DEFLATE application/x-javascript
        AddOutputFilterByType DEFLATE application/x-httpd-php
        AddOutputFilterByType DEFLATE application/x-httpd-fastphp
        AddOutputFilterByType DEFLATE application/x-httpd-eruby
        AddOutputFilterByType DEFLATE text/html

        BrowserMatch ^Mozilla/4 gzip-only-text/html
        BrowserMatch ^Mozilla/4\.0[678] no-gzip
        BrowserMatch \bMSIE !no-gzip !gzip-only-text/html

        # Don't compress images
        SetEnvIfNoCase Request_URI \\.(?:gif|jpe?g|png|swf|swz)$ no-gzip dont-vary
        # or zip files
        SetEnvIfNoCase Request_URI \.zip$ no-gzip dont-vary

        # Make sure proxies don't deliver the wrong content
        Header append Vary User-Agent env=!dont-vary

        DeflateCompressionLevel 5
        DeflateFilterNote ratio

	<Directory "/var/www/html/src/jsonrpc/public">
		Header set Access-Control-Allow-Methods POST
	</Directory>
</VirtualHost>

# vim: syntax=apache ts=4 sw=4 sts=4 sr noet
