# Suggested Configuration for Extensions

## php-cs-fixer

Make sure you install and configure php-cs-fixer correctly for your system. Add the following configuration, however the
rules will use the configuration in the file `.php-cs-fixer.php`.

### Settings

- Tools -> File Watchers
- Add (+) new one
  - Add name "Php-cs-fixer"
  - File type : Php
  - Scope : Current file
  - Program : (path of the php-cs-fixer program, you can run "which php-cs-fixer" to see it )
  - Arguments : "fix --config=$ProjectFileDir$/.php-cs-fixer.php $FileDir$/$FileName$"
    - Just be carefully, if you add $FileDir$/$FileName$ in the arguments is must quicker compare to if you don't have
      anything, <br> but <span style="color:red;font-weight:bold"> THIS WILL IGNORE THE EXCLUDE FILES (FOLDERS) IN
      CONFIG FILE</span>