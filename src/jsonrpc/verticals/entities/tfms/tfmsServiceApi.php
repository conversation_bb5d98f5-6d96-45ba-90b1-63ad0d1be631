<?php

class TfmsServiceApiEntity extends Entities
{

    /**
     * Refresh the access token
     *
     * @return array
     */
    public function refreshToken(): array
    {
        $proxyPath = $this->getApiUrlPath($this->db);
        $server = $_SERVER['HTTP_HOST'];
        // $url = 'https://' . $server . '/' . $proxyPath . '/auth/refresh-token';
        $url = 'https://scdf-tfms.cartrack.com/tfms-services-api/auth/refresh-token';

        $refreshToken = $_COOKIE["refresh_token"];
        // URL decode the refresh token from cookie before sending
        $decodedRefreshToken = urldecode($refreshToken);
        $request = [
            'refreshToken' => $decodedRefreshToken,
        ];
        try {
            $ch = curl_init($url);

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
            curl_setopt(
                $ch,
                CURLOPT_HTTPHEADER,
                array(
                    "Content-Type: application/json",
                    "Content-Length: " . strlen(json_encode($request))
                )
            );

            $result1 = curl_exec($ch);

            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            curl_close($ch);

            if ($http_code == 404) {
                $error["message"] = "Tfms Service refresh token Error : 404 Not found";
                return $error;
            }

            // Set the refresh token to the cookie in the response header
            RefreshTokenService::setRefreshTokenCookie($decodedRefreshToken);
            $result = json_decode($result1, true);
        } catch (Exception $e) {
            $error["message"] = "Tfms Service Refresh token Error : " . $e->getMessage();
            return $error;
        }

        return $result;
    }

    public function revokeToken()
    {
        $proxyPath = $this->getApiUrlPath($this->db);
        $server = $_SERVER['HTTP_HOST'];
        // $url = 'https://' . $server . '/' . $proxyPath . '/auth/revoke-token';
        $url = 'https://scdf-tfms.cartrack.com/tfms-services-api/auth/revoke-token';

        $refreshToken = $_COOKIE["refresh_token"];
        // URL decode the refresh token from cookie before sending
        $decodedRefreshToken = urldecode($refreshToken);
        $request = [
            'refreshToken' => $decodedRefreshToken,
        ];
        try {
            $ch = curl_init($url);

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
            curl_setopt(
                $ch,
                CURLOPT_HTTPHEADER,
                array(
                    "Content-Type: application/json",
                    "Content-Length: " . strlen(json_encode($request))
                )
            );

            curl_exec($ch);

            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            curl_close($ch);

            \setcookie("refresh_token", "", time() - 3600, "/");
            if ($http_code == 404) {
                $error["message"] = "Tfms Service Revoke Token Error : 404 Not found";
                return $error;
            }
        } catch (Exception $e) {
            $error["message"] = "Tfms Service Revoke Token Error : " . $e->getMessage();
            return $error;
        }
    }

    /**
     * Login to Tfms Service Api Login
     * $request['account']
     * $request['username']
     * $request['password']
     * $request['apiKey']
     * 
     * @param $request
     *
     * @throws Exception
     *
     */
    public function TfmsServiceApiLogin($request)
    {
        $proxyPath = $this->getApiUrlPath($this->db);
        $server = $_SERVER['HTTP_HOST'];
        // $url = 'https://' . $server . '/' . $proxyPath . '/auth/login';
        $url = 'https://scdf-tfms.cartrack.com/tfms-services-api/auth/login';
        try {
            $ch = curl_init($url);

            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request));
            curl_setopt(
                $ch,
                CURLOPT_HTTPHEADER,
                array(
                    "Content-Type: application/json",
                    "Content-Length: " . strlen(json_encode($request))
                )
            );

            $result1 = curl_exec($ch);

            $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            curl_close($ch);

            if ($http_code == 404) {
                $error["message"] = "Tfms Service Login Error : 404 Not found";
                return $error;
            }

            $result = json_decode($result1, true);
        } catch (Exception $e) {
            $error["message"] = "Tfms Service Login Error : " . $e->getMessage();
            return $error;
        }

        return $result;
    }

    private function getApiUrlPath($db)
    {
        $rs = pg_query_params($db, "
                      SELECT settings_value
                      FROM fleet.app_settings
                      WHERE settings_name = 'tfmsCartrackServicesAPI' AND
                      enabled
                    ", []);

        if ($rs === false) {
            throw new Exception(ERR_GENERIC);
        }

        $row = pg_fetch_assoc($rs);

        return trim($row['settings_value']);
    }
}
