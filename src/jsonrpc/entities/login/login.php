<?php

use GuzzleHttp\Cookie\CookieJar;
use GuzzleHttp\Cookie\SetCookie;
use TheNetworg\OAuth2\Client\Provider\Azure;
use Firebase\JWT\JWT;
use GuzzleHttp\Client;


define('RESULTS', 'results');
define('NEW_REQUEST', 'newRequest');
define('ERR_FEDERATED_SERVICE_IS_MISSING', 'Federated Service URL is not defined');


class LoginEntity extends SplSubjectBase
{
    /**
     * Simply put: The main login
     *
     * @param resource $db
     * @param SessionManager $sessionManager
     * @param TimezoneUtils $timezoneUtils
     * @param KeyGenerator $keyGenerator
     * @param Encryption $encryption
     * @param MiFleetHelperPOPO $miFleetHelperPOPO
     * @param SisensePOPO $sisensePOPO
     * @param array $data
     */
    public function cartrackLogin($db, $sessionManager, $timezoneUtils, $keyGenerator, $encryption, $miFleetHelperPOPO, $sisensePOPO, $data)
    {
        $mainLogin = new Login($db, $sessionManager, $timezoneUtils, $keyGenerator, $encryption, $miFleetHelperPOPO, $sisensePOPO, $data);
        return $mainLogin->doLogin();
    }

    /**
     * Logs out of the app
     *
     * @return array
     */
    public function logout()
    {
        destroySessionData();
        $tfmsServiceApiEntity = new TfmsServiceApiEntity();
        $tfmsServiceApiEntity->revokeToken();

        return ['logout' => 'ok'];
    }

    /**
     * Fetches a simply array of conditions necessary for the app to load before the loggin
     * - Styles mostly
     *
     * @param bool $isUserLoggedIn
     * @param SessionManager $sessionManager
     *
     * @return array
     */
    public function getPreloginData($isUserLoggedIn, $sessionManager)
    {
        $ret = [];
        $appSettings = [];
        $query_args = [$sessionManager->getServerVar('HTTP_HOST')];

        $cacheKey = CacheControl::buildCacheKey(UsersEntity::OBSERVER_KEY, __FUNCTION__, true, $query_args);
        $cache = CacheControl::getInstance();
        if ($cached = $cache->get($cacheKey)) {
            return $cached;
        }

        // Get Country App Settings
        $result = executeSPCall(
            null,
            $query_args,
            "SELECT * FROM fleet.ct_app_settings_prelogin($1);"
        );

        // Loop app settings into array
        foreach ($result as $value) {
            switch ($value['settingsName']) {
                case 'login_language_json':
                    $ret['languages'] = json_decode($value['settingsValue']);
                    break;
                case 'ct_countries':
                    $ret['ctCountries'] = json_decode($value['settingsValue']);
                    break;
                case 'countriesWebsitesWithHosts':
                    $websitesAndHost = json_decode($value['settingsValue'], true);
                    $allowCountriesWebsite = array_filter($websitesAndHost['hosts'] ?? [], fn($host) => str_contains($_SERVER['HTTP_X_FORWARDED_HOST'] ?? $_SERVER['HTTP_HOST'], $host));
                    $ret['countriesWebsites'] = [];
                    if ($allowCountriesWebsite && !empty($websitesAndHost['countriesWebsites'])) {
                        $ret['countriesWebsites'] = $websitesAndHost['countriesWebsites'];
                    }
                    break;
                case (preg_match('/federated_login_.*/', $value['settingsName']) ? true : false):
                    $provider = str_replace('federated_login_', '', $value['settingsName']);
                    if (!empty($value['settingsValue'])) {
                        $jsonFederated = json_decode($value['settingsValue']);
                        $url = (json_last_error() === 0 && !empty($jsonFederated->federatedService)) ? $jsonFederated->federatedService : null;
                    }
                    $ret['federatedLogins'][] = [
                        'title' => $provider,
                        'key' => $provider,
                        'url' => $url ?? null
                    ];
                    break;
                default:
                    $appSettings[dashesToCamelCase($value['settingsName'])] = $value['settingsValue'];
                    break;
            }
        }

        // Check if the user is logged in
        if ($isUserLoggedIn) {
            // Look for settings from session and use those instead as they would be customized from client_app_settings
            foreach ($appSettings as $key => $value) {
                if (isset($_SESSION["app_settings"][$key])) {
                    $appSettings[$key] = $_SESSION["app_settings"][$key];
                }
            }
        }

        if (defined('SHOW_PHP_VERSION_ON_PRE_LOGIN') && SHOW_PHP_VERSION_ON_PRE_LOGIN && str_lower(TYPE_ENVIRONMENT) != 'production') {
            $ret['phpVersion'] = phpversion();
        }

        $ret['appSettings'] = $appSettings;
        $cache->put($cacheKey, $ret, CacheControl::CACHE_EXPIRY_DAY);

        return $ret;
    }

    /**
     * Fetches the login destination URL for the federated provider.
     *
     * @param string $providerName - Lower case string identifying the provider.
     * @param SessionManager $sessionManager
     *
     * @return array
     * @throws Exception
     *
     */
    public function getFederatedLoginUrl($providerName, $sessionManager)
    {
        $ret = [];
        // Get App Settings
        $result = executeSPCall(
            null,
            [
                $sessionManager->getServerVar('HTTP_HOST'),
                'federated_login_' . strtolower($providerName),
            ],
            "SELECT * FROM fleet.ct_app_settings_prelogin($1) WHERE out_settings_name = $2;"
        );
        if (empty($result)) {
            throw new Exception(ERR_GENERIC);
        }
        // Decode json containing assoc array of clientId, clientSecret and redirectUri (optional).
        $f_provider = json_decode($result[0]['settingsValue'], true);
        $federated_login_redirect_uri_override = defined('FEDERATED_LOGIN_REDIRECT_URI_OVERRIDE') ? FEDERATED_LOGIN_REDIRECT_URI_OVERRIDE : false;
        if ($federated_login_redirect_uri_override || empty($f_provider['redirectUri'])) {
            $parsedUrl = parse_url($sessionManager->getReferer());
            $f_provider['redirectUri'] = $parsedUrl['scheme'] . '://' . $parsedUrl['host'];
            $f_provider['redirectUri'] .= (!empty($parsedUrl['port'])) ? ':' . $parsedUrl['port'] : '';
            $f_provider['redirectUri'] .= '/login';
        }
        $f_provider['scopes'] = ['openid'];
        $provider = false;

        $providerClass = null;
        switch ($providerName) {
            case 'azure':
                $provider = true;
                $providerClass = 'TheNetworg\OAuth2\Client\Provider\Azure';
                break;
            case 'spf':
            case 'tfms':
                if (!empty($f_provider['federatedService'])) {
                    $providerClass = $f_provider['federatedService'];
                } else {
                    throw new Exception(ERR_FEDERATED_SERVICE_IS_MISSING);
                }
                break;
        }

        if ($provider) {
            /** @var TheNetworg\OAuth2\Client\Provider\Azure */
            $provider = new $providerClass($f_provider);
            $ret['url'] = $provider->getAuthorizationUrl();

            // $_SESSION['oauth2state'] allows for verification of login method on return.
            $oauth2state = [$provider->getState() => $providerName];
            $sessionManager->setSessionValue('oauth2state', $oauth2state);
        } else {
            $ret['url'] = str_replace('URL', urlencode($f_provider['redirectUri']), $providerClass);
        }
        return $ret;
    }

    /**
     * @param resource $db
     * @param int $userID
     * @param int $currentUserID
     * @param SessionManager $sessionManager
     * @param string $defaultCountry
     * @param TimezoneUtils $timelineUtils
     * @param mixed $currentDayHour
     *
     */
    public function getLoginSequence($db, $userID, $currentUserID, $sessionManager, $defaultCountry, $timelineUtils, $currentDayHour)
    {
        $loginSequence = new LoginSequence($db, $userID, $currentUserID, $sessionManager, $defaultCountry, $timelineUtils, $currentDayHour);
        return $loginSequence->buildLoginSequence();
    }

    /**
     * ct_token_login
     * Used for vehicle location sharing links, tokens are not OTPS. OTPs use ct_login
     *
     * @param SessionManager $sessionManager
     * @param KeyGenerator $keyGenerator
     * @param string $userID ct.user username
     * @param string $clientUserID fleet.client_user user_id
     * @param string $tToken authentication token for token login
     * @param mixed $tVehicle vehicle_id if applicable
     * @param string $browserName
     *
     * @return array
     * @throws Exception
     *
     */
    public function tokenLogin($sessionManager, $keyGenerator, $userID, $clientUserID, $tToken, $tVehicle, $browserName = '')
    {
        $ret = [];
        $_SESSION["auth"] = 0;
        $userID = trim($userID);
        $db = connectToProperServer($userID);
        $clientUserID = trim($clientUserID);
        $clientUserID = $clientUserID == '0' ? '' : $clientUserID;
        $passKey = trim($tToken);
        $vehicle_id = $tVehicle == "" ? null : trim($tVehicle);

        $query = "SELECT user_name, server_id, user_type_id FROM ct.user WHERE user_id = $1";
        $rs = pg_query_params($db, $query, [$userID]);

        if ($rs === false) {
            throw new Exception(ERR_GENERIC);
        }

        $store = pg_fetch_all($rs);
        if (empty($store)) {
            throw new Exception(ERR_GENERIC);
        }

        $user_name = $store[0]['user_name'];
        $server_id = $store[0]['server_id'];
        $is_third_party = ($store[0]['user_type_id'] == USER_TYPE_FLEET_USER);

        $validKey = false;
        if ($vehicle_id == null) {
            $rs2 = pg_query_params($db, "
                    SELECT login_token FROM fleet.client_user_settings WHERE user_id = $1 AND client_user_id = $2
                ", [
                $userID,
                $clientUserID,
            ]);

            // @phpstan-ignore argument.type
            if ($rowToken = pg_fetch_assoc($rs2)) {
                if (isset($rowToken['login_token']) && $rowToken['login_token'] == $passKey) {
                    $validKey = true;
                }
            }
        } else {
            $rs2 = pg_query_params($db, "
                    SELECT expire_ts, login_token, user_id, client_user_id AS c FROM fleet.client_vehicle_link
                    WHERE user_id = $1
                    AND coalesce(client_user_id,'') = $2
                    AND vehicle_id = $3
                    AND login_token = $4
                    AND expire_ts >= CURRENT_TIMESTAMP
                ", [
                $userID,
                $clientUserID,
                $vehicle_id,
                $passKey,
            ]);
            // @phpstan-ignore argument.type
            if ($rowVehicleToken = pg_fetch_assoc($rs2)) {
                // @phpstan-ignore argument.type
                if (pg_numrows($rs2) != 0) {
                    $validKey = true;
                }
            }
        }
        if ($validKey) {
            $sessionManager->logGuestInAsUser(
                $user_name,
                $userID,
                $clientUserID,
                $server_id
            );
            $_SESSION["auth"] = 1;
            $_SESSION["is_third_party"] = $is_third_party;
            // @phpstan-ignore variable.undefined
            $_SESSION["auth_token_expires"] = $rowVehicleToken['expire_ts'];
            $_SESSION["token_login_enabled"] = true;

            $ret["status"] = LOGIN_SUCCEEDED;
            $ret["username"] = $user_name;
            $ret["user_id"] = $userID;
            $ret["client_user_id"] = $clientUserID == null ? "" : $clientUserID;

            $users = new UsersEntity();
            $settings = new SettingsEntity();
            $geofenceEntity = new GeofenceEntity();

            $getUserSettings = $users->getUserSettings($userID, $clientUserID, $sessionManager);
            $ret['ct_fleet_get_user_settings'] = [
                'customer_name' => $getUserSettings["ct_fleet_get_user_settings"]['customer_name'] ?? '',
            ];

            $settings->getAppSettings();
            $ret['ct_fleet_get_app_settings'] = $sessionManager->getAppSettings();

            $geofenceEntity->getGisVeGisVersion($db, $sessionManager);

            // record login history
            pg_query_params($db, "
                    INSERT INTO fleet.client_login_history(
                    event_ts, client_id, sub_client_id, login_source_id, login_source_ip,client_details)
                    VALUES (current_timestamp, $1, $2, 3, $3,$4);
                ", [
                $userID,
                $clientUserID,
                getClientIP(),
                $browserName,
            ]);

            return $ret;
        }
        sleep(5); // bruteforce slowdown
        $ret["status"] = ERR_LOGIN_CREDENTIALS;
        return $ret;
    }

    /**
     * Logouts as user, reloggins as subuser argument
     *
     * @param resource $db
     * @param int $userID
     * @param string $userAccount
     * @param int $subUserID
     *
     * @return string
     * @throws Exception
     *
     */
    public function loginAsSubUser($db, $userID, $userAccount, $subUserID)
    {
        $this->logout();

        $sql = "SELECT password_hash, user_name, client_user_id FROM fleet.client_user WHERE user_id = $1 AND client_user_id = $2";
        $params = [$userID, $subUserID];
        $rs = pg_query_params($db, $sql, $params);

        if ($rs === false) {
            throw new Exception(ERR_GENERIC);
        }

        $row = pg_fetch_all($rs);
        if (empty($row)) {
            throw new Exception(ERR_GENERIC);
        }

        $passHash = $row[0]['password_hash'];
        $client_user_id = $row[0]['client_user_id'];
        $client_user_name = $row[0]['user_name'];
        $tomorrowTS = time() + SECONDS_IN_A_DAY;

        $token = [
            "account" => $userAccount,
            "password_hash" => $passHash,
            "client_user_id" => $client_user_id,
            "client_user_name" => $client_user_name,
            "expiration" => $tomorrowTS,
        ];

        return JWT::encode($token, JWT_SECRET_KEY);
    }

    /**
     * Login for intendia
     *
     * @param resource $db
     * @param bool   $intendiaAllowed
     * @param IntendiaHelperPOPO $intendiaHelperPOPO
     *
     * @return array|bool|string
     * @throws Exception
     *
     */
    public function intendiaLogin($db, $intendiaAllowed, $intendiaHelperPOPO)
    {
        if ($intendiaAllowed) {
            $intendia = new intendia($db, $intendiaHelperPOPO);
            $token = $intendia->intendia_login();

            if ($token === false) {
                $token = [];
            }

            return $token;
        }
        return [];
    }

    /**
     * Given a an email or SMS, it generates an OTP for the given parameter
     *
     * @param mixed $observerServer
     * @param mixed $logWriter
     * @param string $parameter
     * @param string $countryCode
     * @param int $userID
     *
     * @throws Exception
     */
    public function getOTPByEmailOrSMS($observerServer, $logWriter, $parameter, $countryCode, $userID)
    {
        $getLoginDetailsHelper = new LoginDetailsHelper();
        return $getLoginDetailsHelper->processOTP($observerServer, $logWriter, $parameter, $countryCode, $userID);
    }

    /**
     * Matches the OTP for the given paramter (SMS or Email)
     *
     * @param string $parameter
     * @param mixed $otp
     * @param string $countryCode
     * @param mixed $userID
     *
     * @return array|string
     * @throws Exception
     *
     */
    public function matchEmailOrSMSOTP($parameter, $otp, $userID, $countryCode)
    {
        $getLoginDetailsHelper = new LoginDetailsHelper();
        return $getLoginDetailsHelper->matchOTP($parameter, $otp, $userID, $countryCode);
    }

    /**
     * Splits the Sing-Up into helper methods for personal and business
     *
     * @param resource $db
     * @param array $data
     *
     * @throws Exception
     */
    public function signUp($db, $data)
    {
        $getLoginDetailsHelper = new LoginDetailsHelper();
        $type = $data['type'];

        if ($type === SIGN_UP_TYPE_PERSONAL) {
            return $getLoginDetailsHelper->signUpPersonal($db, $data);
        } elseif ($type === SIGN_UP_TYPE_BUSINESS) {
            return $getLoginDetailsHelper->signUpBusiness($db, $data);
        } else {
            throw new Exception(ERR_GENERIC);
        }
    }

    /**
     * The complete procedure to change a password
     *
     * @param resource $db
     * @param string $securityCheck
     * @param string $password
     * @param mixed $serverList
     * @param mixed $miFleetDB
     *
     * @return array{status: bool, is_two_factor_activated: bool, ct_fleet_mobile_only_contract: bool}
     * @throws Exception
     */
    public function changePasswordProcedure($db, $securityCheck, $serverList, $miFleetDB, $password)
    {
        $ignoreCurrentPassword = true;
        $userEntity = new UsersEntity();

        $resetContext = $userEntity->getSecurityCheckCodeContext($serverList, $securityCheck, $password);

        $userEntity->userSetPassword($db, $resetContext['user_id'], $resetContext['client_user_id'], $serverList, $password, '', $miFleetDB, $ignoreCurrentPassword);

        connectToProperServer($resetContext['user_id']);
        $twoFactorAuthEntity = new TwoFactorAuthEntity();
        $twoFactorUserInfo = $twoFactorAuthEntity->checkIfTwoFactorAuthEnabled($resetContext['user_id'], '0');

        $hasMobileOnlyContracts = $userEntity->hasActiveMobileOnlyContracts($serverList);

        return [
            'status' => true,
            'is_two_factor_activated' => $twoFactorUserInfo ? true : false,
            'ct_fleet_mobile_only_contract' => $hasMobileOnlyContracts
        ];
    }


    public function refreshToken(): array
    {
        $tfmsServiceApiEntity = new TfmsServiceApiEntity();
        return $tfmsServiceApiEntity->refreshToken();
    }
}
