# Suggested Configuration for Extensions

## php-cs-fixer
Make sure you install and configure php-cs-fixer correctly for your system. Add the following configuration, however the rules will use the configuration in the file `.php-cs-fixer.php`.
```
    "php-cs-fixer.rules": "@PSR12",
    "php-cs-fixer.pathMode": "override",
    "php-cs-fixer.executablePath": "/your/path/to/php-cs-fixer",
    "php-cs-fixer.onsave": true,
```

## XDebug Configuration
Copy `.vscode/launch.example.json` to `.vscode/launch.json` and make any edits to "Listen for Xdebug".

When you have your container built with this repo's cocker-compose and you enable "Listen for Xdebug" under "Run and Debug" in VSCode then the IDE will break on breakpoints or errors.
